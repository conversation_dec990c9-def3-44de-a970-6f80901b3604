<script setup>
  import { round } from 'lodash-es'
  import { updateEquip } from '@/api/graph/index.js'
  import { ElMessage } from 'element-plus'
  import { reactive, ref, watch, nextTick, onUnmounted } from 'vue'
  import * as THREE from 'three'
  import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

  const props = defineProps({
    detailInfo: {
      type: Object,
      default: () => {}
    },
    editFlag: {
      type: Boolean,
      default: false
    }
  })

  // 定义默认的 detailInfo 结构
  const defaultDetailInfo = [
    {
      label: '调制类型',
      value: '',
      key: 'modulationType', // editType: 'input' 或 'select'，默认给它 'select' 做演示
      editType: 'select',
      // 如果是枚举，就可以配置 options
      options: [
        { label: 'AM', value: 'AM' },
        { label: 'FM', value: 'FM' },
        { label: 'PSK', value: 'PSK' }
      ],
      // 如果要做枚举值 <-> 文本的更复杂转换，可以定义 enumConverse
      enumConverse: {
        display: rawVal => {
          // rawVal 可能是 'AM'/'FM'/'PSK'，如果你需要更复杂映射就写在这里
          return rawVal
        },
        store: displayVal => {
          // 这里可以做相反的映射，如果有需要的话
          return displayVal
        }
      }
    },
    {
      label: '调制方式',
      value: '',
      key: 'modulationMethod',
      editType: 'select',
      // 如果是枚举，就可以配置 options
      options: [
        { label: 'AM', value: 'AM' },
        { label: 'FM', value: 'FM' },
        { label: 'PSK', value: 'PSK' }
      ],
      // 如果要做枚举值 <-> 文本的更复杂转换，可以定义 enumConverse
      enumConverse: {
        display: rawVal => {
          // rawVal 可能是 'AM'/'FM'/'PSK'，如果你需要更复杂映射就写在这里
          return rawVal
        },
        store: displayVal => {
          // 这里可以做相反的映射，如果有需要的话
          return displayVal
        }
      }
    },
    { label: '调制系数', value: '', key: 'modulationCoefficient', editType: 'input' },
    {
      label: '频率下限（MHz）',
      value: '',
      key: 'freqDown',
      editType: 'input',
      unitConverse: {
        display: rawVal => {
          // rawVal: 原始(Hz)
          return round(rawVal / 1_000_000, 6) // 显示 MHz
        },
        restore: displayVal => {
          // displayVal: 用户编辑的 MHz
          return parseFloat(displayVal) * 1_000_000
        }
      }
    },
    {
      label: '频率上限（MHz）',
      value: '',
      key: 'freqUp',
      editType: 'input',
      unitConverse: {
        display: rawVal => {
          // rawVal: 原始(Hz)
          return round(rawVal / 1_000_000, 6) // 显示 MHz
        },
        restore: displayVal => {
          // displayVal: 用户编辑的 MHz
          return parseFloat(displayVal) * 1_000_000
        }
      }
    },
    { label: '信号占用度带宽', value: '', key: 'bw', editType: 'input' },
    { label: '最大传输速率', value: '', key: 'maxSpeed', editType: 'input' },
    { label: '有效传输距离', value: '', key: 'effectiveDistance', editType: 'input' },
    { label: '信道数量', value: '', key: 'channelNum', editType: 'input' },
    { label: '最低信道中心频率', value: '', key: 'minChannelCf', editType: 'input' },
    { label: '峰包功率', value: '', key: 'peakEnvelopePw', editType: 'input' },
    { label: '平均功率', value: '', key: 'avgPw', editType: 'input' },
    { label: '载波功率', value: '', key: 'carrierWavePw', editType: 'input' },
    {
      label: '能否跳扩频',
      value: '',
      key: 'iskt',
      editType: 'select',
      // 如果是枚举，就可以配置 options
      options: [
        { label: 'AM', value: 'AM' },
        { label: 'FM', value: 'FM' },
        { label: 'PSK', value: 'PSK' }
      ],
      // 如果要做枚举值 <-> 文本的更复杂转换，可以定义 enumConverse
      enumConverse: {
        display: rawVal => {
          // rawVal 可能是 'AM'/'FM'/'PSK'，如果你需要更复杂映射就写在这里
          return rawVal
        },
        store: displayVal => {
          // 这里可以做相反的映射，如果有需要的话
          return displayVal
        }
      }
    },
    {
      label: '3dB带宽（kHz）',
      value: '',
      key: 'threeBw',
      editType: 'input',
      unitConverse: {
        display: rawVal => {
          // rawVal: 原始(Hz)
          return round(rawVal / 1_000, 3) // 显示 MHz
        },
        restore: displayVal => {
          // displayVal: 用户编辑的 MHz
          return parseFloat(displayVal) * 1_000
        }
      }
    },
    {
      label: '60dB带宽（kHz）',
      value: '',
      key: 'sixtyBw',
      editType: 'input',
      unitConverse: {
        display: rawVal => {
          // rawVal: 原始(Hz)
          return round(rawVal / 1_000, 3) // 显示 MHz
        },
        restore: displayVal => {
          // displayVal: 用户编辑的 MHz
          return parseFloat(displayVal) * 1_000
        }
      }
    },
    {
      label: '20dB带宽（kHz）',
      value: '',
      key: 'twentyBw',
      editType: 'input',
      unitConverse: {
        display: rawVal => {
          // rawVal: 原始(Hz)
          return round(rawVal / 1_000, 3) // 显示 MHz
        },
        restore: displayVal => {
          // displayVal: 用户编辑的 MHz
          return parseFloat(displayVal) * 1_000
        }
      }
    },
    { label: '功率可调性', value: '', key: 'pwModifiability', editType: 'input' },
    { label: '频率容限', value: '', key: 'freqTolerance', editType: 'input' },
    { label: '频率可调性', value: '', key: 'freqModifiability', editType: 'input' },
    {
      label: '多路多址方式',
      value: '',
      key: 'fdma',
      editType: 'select',
      // 如果是枚举，就可以配置 options
      options: [
        { label: 'AM', value: 'AM' },
        { label: 'FM', value: 'FM' },
        { label: 'PSK', value: 'PSK' }
      ],
      // 如果要做枚举值 <-> 文本的更复杂转换，可以定义 enumConverse
      enumConverse: {
        display: rawVal => {
          // rawVal 可能是 'AM'/'FM'/'PSK'，如果你需要更复杂映射就写在这里
          return rawVal
        },
        store: displayVal => {
          // 这里可以做相反的映射，如果有需要的话
          return displayVal
        }
      }
    },
    {
      label: '40dB带宽（kHz）',
      value: '',
      key: 'fortyBw',
      editType: 'input',
      unitConverse: {
        display: rawVal => {
          // rawVal: 原始(Hz)
          return round(rawVal / 1_000, 3) // 显示 MHz
        },
        restore: displayVal => {
          // displayVal: 用户编辑的 MHz
          return parseFloat(displayVal) * 1_000
        }
      }
    }
  ]

  // 使用 reactive 创建内部的 detailInfo 状态
  const internalDetailInfo = reactive(defaultDetailInfo.map(item => ({ ...item })))

  const dialogTableVisible = defineModel({ type: Boolean, default: false })

  const IMGArr = ref({
    equipmentDiagram: null,
    directionalPattern: null,
    threeDimensional: null
  })

  const activeName = ref('first')

  const confirm = () => {
    dialogTableVisible.value = false
  }

  const onBlur = row => {
    const val = parseFloat(row.tempValue)
    if (isNaN(val)) {
      row.value = 0
      return
    }

    // 如果有 unitConverse，则用 restore() 做乘法、除法等
    if (row.unitConverse) {
      row.value = row.unitConverse.restore(val)
    } else {
      row.value = val
    }
  }

  const onSelectChange = row => {
    // row.tempValue: 用户在下拉中选的枚举
    if (row.enumConverse) {
      // 如果要做更复杂的映射
      row.value = row.enumConverse.store(row.tempValue)
    } else {
      row.value = row.tempValue
    }
  }

  /**
   * 监听 props.detailInfo 的变化，并更新 internalDetailInfo 中对应的值
   */
  watch(
    () => props.detailInfo,
    newFileInfo => {
      for (const key in newFileInfo) {
        if (
          key === 'equipmentDiagram' ||
          key === 'directionalPattern' ||
          key === 'threeDimensional'
        ) {
          IMGArr.value[key] = newFileInfo[key]

          // 如果是三维模型且当前在三维模型tab，立即加载模型
          if (key === 'threeDimensional' && newFileInfo[key] && activeName.value === 'third') {
            nextTick(() => {
              if (!scene) {
                initThreeScene()
              }
              // 处理base64数据或URL
              const modelUrl = newFileInfo[key].startsWith('data:')
                ? newFileInfo[key]
                : newFileInfo[key]
              loadGLBModel(modelUrl)
            })
          }
        }
        const existingItem = internalDetailInfo.find(item => item.key === key)
        if (existingItem) {
          // 1. 原始值写到 existingItem.value
          existingItem.value = newFileInfo[key]

          // 2. 初始化 tempValue，供编辑使用
          if (existingItem.editType === 'select') {
            // 如果是枚举类型，优先走 enumConverse
            if (existingItem.enumConverse) {
              existingItem.tempValue = existingItem.enumConverse.display(newFileInfo[key])
            } else {
              // 如果没有 enumConverse，就默认把原始值直接给 tempValue
              existingItem.tempValue = newFileInfo[key]
            }
          } else {
            // editType === 'input'
            if (existingItem.unitConverse) {
              existingItem.tempValue = existingItem.unitConverse.display(newFileInfo[key])
            } else {
              existingItem.tempValue = newFileInfo[key]
            }
          }
        }
      }
    },
    { deep: true, immediate: true }
  )

  // 添加文件转Base64的工具函数
  const fileToBase64 = file => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  }

  // 三维模型相关
  const threeDModelContainer = ref(null)
  let scene, camera, renderer, controls

  // 初始化Three.js场景
  const initThreeScene = () => {
    if (!threeDModelContainer.value) return

    // 场景
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0xf0f0f0)

    // 相机
    camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000)
    camera.position.set(0, 0, 5)

    // 渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(400, 400)
    renderer.shadowMap.enabled = true
    threeDModelContainer.value.appendChild(renderer.domElement)

    // 控制器
    controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true

    // 光源
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
    scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(1, 1, 1)
    scene.add(directionalLight)

    animate()
  }

  // 动画循环
  const animate = () => {
    requestAnimationFrame(animate)
    controls.update()
    renderer.render(scene, camera)
  }

  // 加载GLB模型
  const loadGLBModel = url => {
    // 检查scene是否已初始化
    if (!scene) {
      console.warn('Three.js scene not initialized yet')
      return
    }

    const loader = new GLTFLoader()

    // 清除之前的模型
    scene.children = scene.children.filter(
      child => child.type === 'AmbientLight' || child.type === 'DirectionalLight'
    )

    // 处理不同类型的URL
    let modelUrl = url

    // 如果是base64数据，需要转换为blob URL
    if (url.startsWith('data:')) {
      try {
        // 从base64创建blob
        const response = fetch(url)
        response
          .then(res => res.blob())
          .then(blob => {
            modelUrl = URL.createObjectURL(blob)
            loadModel(modelUrl)
          })
        return
      } catch (error) {
        console.error('Failed to convert base64 to blob:', error)
        return
      }
    }

    loadModel(modelUrl)

    function loadModel(finalUrl) {
      loader.load(
        finalUrl,
        gltf => {
          const model = gltf.scene
          scene.add(model)

          // 调整模型大小和位置
          const box = new THREE.Box3().setFromObject(model)
          const center = box.getCenter(new THREE.Vector3())
          const size = box.getSize(new THREE.Vector3())

          const maxDim = Math.max(size.x, size.y, size.z)
          const scale = 2 / maxDim
          model.scale.setScalar(scale)

          model.position.sub(center.multiplyScalar(scale))
        },
        progress => {
          console.log('Loading progress:', progress)
        },
        error => {
          console.error('Error loading GLB model:', error)
          ElMessage.error('模型加载失败')
        }
      )
    }
  }

  // 处理文件上传
  const handleUpload = async (file, imageType) => {
    try {
      if (imageType === 'threeDimensional') {
        // 三维模型处理
        const url = URL.createObjectURL(file.raw)
        IMGArr.value[imageType] = url

        // 如果当前在三维模型tab，立即显示模型
        if (activeName.value === 'third') {
          nextTick(() => {
            // 确保场景已初始化
            if (!scene) {
              initThreeScene()
            }
            loadGLBModel(url)
          })
        }

        // 转换为Base64用于存储
        const base64 = await fileToBase64(file.raw)

        const updateData = {
          id: props.detailInfo.id,
          [imageType]: base64
        }

        await updateEquip(updateData).then(res => {
          if (res.code === 200) {
            ElMessage.success('上传成功')
          }
        })
      } else {
        // 图片处理
        const base64 = await fileToBase64(file.raw)
        IMGArr.value[imageType] = base64

        const updateData = {
          id: props.detailInfo.id,
          [imageType]: base64
        }

        await updateEquip(updateData).then(res => {
          if (res.code === 200) {
            ElMessage.success('上传成功')
          }
        })
      }
    } catch (error) {
      console.error('Upload error:', error)
      ElMessage.error('上传失败')
    }
  }

  // 监听tab切换，初始化Three.js场景
  const handleClick = tab => {
    if (tab.name === 'third') {
      nextTick(() => {
        // 确保场景已初始化
        if (!scene) {
          initThreeScene()
        }

        // 如果有三维模型数据，加载它
        if (IMGArr.value.threeDimensional) {
          loadGLBModel(IMGArr.value.threeDimensional)
        }
      })
    }
  }

  // 组件卸载时清理Three.js资源
  onUnmounted(() => {
    if (renderer) {
      renderer.dispose()
    }
    if (scene) {
      scene.clear()
    }
  })
</script>

<template>
  <el-dialog v-model="dialogTableVisible" :title="detailInfo.name" width="1200" align="center">
    <el-row :gutter="5">
      <el-col :span="16">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane label="装备图" name="first">
            <el-upload
              :auto-upload="false"
              :show-file-list="false"
              :on-change="file => handleUpload(file, 'equipmentDiagram')"
              accept="image/*"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
            <el-empty v-if="!IMGArr.equipmentDiagram"></el-empty>
            <img v-else :src="IMGArr.equipmentDiagram" style="width: 100%; height: 100%" />
          </el-tab-pane>
          <el-tab-pane label="方向图" name="second">
            <el-upload
              :auto-upload="false"
              :show-file-list="false"
              :on-change="file => handleUpload(file, 'directionalPattern')"
              accept="image/*"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
            <el-empty style="height: 100%" v-if="!IMGArr.directionalPattern"></el-empty>
            <img v-else :src="IMGArr.directionalPattern" style="width: 100%; height: 100%" />
          </el-tab-pane>
          <el-tab-pane
            label="三维模型"
            name="third"
            style="height: 100%; display: flex; flex-direction: column"
          >
            <el-upload
              :auto-upload="false"
              :show-file-list="false"
              :on-change="file => handleUpload(file, 'threeDimensional')"
              accept=".glb,.gltf"
              style="margin-bottom: 10px"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
            <div style="flex: 1; display: flex; align-items: center; justify-content: center">
              <el-empty v-if="!IMGArr.threeDimensional" style="height: 100%">
                <template #description>
                  <span>暂无三维模型</span>
                </template>
              </el-empty>
              <div
                v-else
                ref="threeDModelContainer"
                style="
                  width: 100%;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              ></div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col style="height: 600px; overflow-y: scroll" :span="8">
        <vxe-table
          border
          :show-header="false"
          :data="internalDetailInfo"
          :edit-config="editFlag ? { trigger: 'click', mode: 'cell' } : null"
        >
          <!-- 参数名列 -->
          <vxe-column field="label" title="参数名" width="160" align="center"></vxe-column>

          <!-- 参数值列，可编辑 -->
          <vxe-column
            field="value"
            title="参数值"
            :edit-render="{ autofocus: '.vxe-input--inner' }"
            align="center"
          >
            <!-- 默认展示：根据类型，做合适的展示 -->
            <template #default="{ row }">
              <!-- 如果是枚举类型并且有 enumConverse，就优先使用它来展示；否则看是否有 unitConverse，再否则直接 row.value -->
              <span v-if="row.editType === 'select' && row.enumConverse">
                {{ row.enumConverse.display(row.value) }}
              </span>
              <span v-else-if="row.unitConverse">
                {{ row.unitConverse.display(row.value) }}
              </span>
              <span v-else>
                {{ row.value }}
              </span>
            </template>

            <!-- 编辑模式：根据 editType 区分 input / select -->
            <template #edit="{ row }">
              <template v-if="row.editType === 'input'">
                <vxe-input v-model="row.tempValue" type="text" @blur="onBlur(row)"></vxe-input>
              </template>
              <template v-else-if="row.editType === 'select'">
                <vxe-select v-model="row.tempValue" @change="onSelectChange(row)">
                  <vxe-option
                    v-for="opt in row.options"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  ></vxe-option>
                </vxe-select>
              </template>
              <!-- 其他类型可自行扩展 -->
            </template>
          </vxe-column>
        </vxe-table>
      </el-col>
    </el-row>

    <template #footer>
      <div class="text-center">
        <vxe-button @click="confirm">取消</vxe-button>
        <vxe-button status="primary" @click="confirm">确认</vxe-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
  :deep(.el-tabs__item) {
    flex: 1;
  }
  :deep(.el-tabs__nav) {
    float: none !important;
  }
</style>
